
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UIKit", "UIKit.csproj", "{b7c661f6-5103-8a03-ab6f-07de25c6b73d}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ResKit", "ResKit.csproj", "{5ca1afcf-e80e-61e1-d5d3-2fcae10335b1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XCharts.Runtime", "XCharts.Runtime.csproj", "{1ecacdcb-541f-4fd6-4d2b-3c1cc115af3f}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QFramework.CoreKit", "QFramework.CoreKit.csproj", "{a44313b5-947b-1876-bedf-10a33704c891}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SupportOldQF", "SupportOldQF.csproj", "{3c14cf64-2080-ed77-7504-0d5e24aa9a4a}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XCharts.Editor", "XCharts.Editor.csproj", "{2749d159-ba00-0971-305f-1328fcb2c018}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{e5d9787f-247a-392e-566e-ed4ab1a580da}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UIKit.Editor", "UIKit.Editor.csproj", "{8400e358-ecec-48bf-05e0-9bae4407098e}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ResKit.Editor", "ResKit.Editor.csproj", "{70cd76e8-2785-62d4-c6b4-ed8e1430f1cf}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XCharts.Examples", "XCharts.Examples.csproj", "{063449ba-e268-1308-fe46-86a47c867109}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{40130bf8-ce07-cc07-73a2-4b9691b061cb}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AudioKit", "AudioKit.csproj", "{32eca7c9-502a-103e-c642-0f721c17d816}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QFramework", "QFramework.csproj", "{f8b4cba7-22ea-46f9-fa43-260194d4e6e9}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{b7c661f6-5103-8a03-ab6f-07de25c6b73d}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{b7c661f6-5103-8a03-ab6f-07de25c6b73d}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5ca1afcf-e80e-61e1-d5d3-2fcae10335b1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5ca1afcf-e80e-61e1-d5d3-2fcae10335b1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1ecacdcb-541f-4fd6-4d2b-3c1cc115af3f}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1ecacdcb-541f-4fd6-4d2b-3c1cc115af3f}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{a44313b5-947b-1876-bedf-10a33704c891}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{a44313b5-947b-1876-bedf-10a33704c891}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3c14cf64-2080-ed77-7504-0d5e24aa9a4a}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3c14cf64-2080-ed77-7504-0d5e24aa9a4a}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2749d159-ba00-0971-305f-1328fcb2c018}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2749d159-ba00-0971-305f-1328fcb2c018}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{e5d9787f-247a-392e-566e-ed4ab1a580da}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e5d9787f-247a-392e-566e-ed4ab1a580da}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8400e358-ecec-48bf-05e0-9bae4407098e}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8400e358-ecec-48bf-05e0-9bae4407098e}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70cd76e8-2785-62d4-c6b4-ed8e1430f1cf}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70cd76e8-2785-62d4-c6b4-ed8e1430f1cf}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{063449ba-e268-1308-fe46-86a47c867109}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{063449ba-e268-1308-fe46-86a47c867109}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40130bf8-ce07-cc07-73a2-4b9691b061cb}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40130bf8-ce07-cc07-73a2-4b9691b061cb}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{32eca7c9-502a-103e-c642-0f721c17d816}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{32eca7c9-502a-103e-c642-0f721c17d816}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{f8b4cba7-22ea-46f9-fa43-260194d4e6e9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{f8b4cba7-22ea-46f9-fa43-260194d4e6e9}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
