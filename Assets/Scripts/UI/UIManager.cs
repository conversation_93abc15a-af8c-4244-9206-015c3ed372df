using UnityEngine;
using QFramework;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// UI管理器 - 统一管理所有UI面板和组件
    /// </summary>
    public class UIManager : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController
    {
        [Header("UI面板引用")]
        [SerializeField] private EnergyDashboardPanel mainPanel;
        [SerializeField] private GameObject loadingPanel;
        [SerializeField] private GameObject errorPanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject fullscreenChartPanel;
        
        [Header("UI设置")]
        [SerializeField] private float panelTransitionDuration = 0.3f;
        [SerializeField] private Ease panelTransitionEase = Ease.OutQuart;
        [SerializeField] private bool enableUIAnimations = true;
        
        [Header("响应式设计")]
        [SerializeField] private Vector2 minResolution = new Vector2(1280, 720);
        [SerializeField] private Vector2 maxResolution = new Vector2(3840, 2160);
        [SerializeField] private bool autoAdjustLayout = true;
        
        private Dictionary<string, GameObject> uiPanels = new Dictionary<string, GameObject>();
        private IEnergyDashboardModel mModel;
        private string currentActivePanel = "main";
        private Vector2 lastScreenSize;
        
        private void Awake()
        {
            // 初始化UI面板字典
            InitializePanelDictionary();
            
            // 设置初始屏幕尺寸
            lastScreenSize = new Vector2(Screen.width, Screen.height);
        }
        
        private void Start()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 初始化主面板
            InitializeMainPanel();
            
            // 绑定数据和事件
            BindEvents();
            
            // 显示加载界面
            ShowLoadingPanel();
            
            // 开始加载数据
            LoadInitialData();
        }
        
        private void InitializePanelDictionary()
        {
            uiPanels["main"] = mainPanel?.gameObject;
            uiPanels["loading"] = loadingPanel;
            uiPanels["error"] = errorPanel;
            uiPanels["settings"] = settingsPanel;
            uiPanels["fullscreen"] = fullscreenChartPanel;
            
            // 初始状态下隐藏所有面板
            foreach (var panel in uiPanels.Values)
            {
                if (panel != null)
                {
                    panel.SetActive(false);
                }
            }
        }
        
        private void InitializeMainPanel()
        {
            if (mainPanel != null)
            {
                // 主面板会在数据加载完成后显示
                mainPanel.gameObject.SetActive(false);
            }
        }
        
        private void BindEvents()
        {
            // 监听数据加载状态
            mModel.IsLoading.RegisterWithInitValue(OnLoadingStateChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            mModel.HasError.RegisterWithInitValue(OnErrorStateChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            // 监听UI相关命令
            this.RegisterEvent<ShowFullscreenChartEvent>(OnShowFullscreenChart)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            this.RegisterEvent<ShowSettingsPanelEvent>(OnShowSettingsPanel)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            this.RegisterEvent<HidePanelEvent>(OnHidePanel)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        private void LoadInitialData()
        {
            // 发送初始化数据加载命令
            this.SendCommand<InitializeDataCommand>();
        }
        
        private void OnLoadingStateChanged(bool isLoading)
        {
            if (isLoading)
            {
                ShowLoadingPanel();
            }
            else
            {
                HideLoadingPanel();
                ShowMainPanel();
            }
        }
        
        private void OnErrorStateChanged(bool hasError)
        {
            if (hasError)
            {
                ShowErrorPanel();
            }
            else
            {
                HideErrorPanel();
            }
        }
        
        private void OnShowFullscreenChart(ShowFullscreenChartEvent e)
        {
            ShowFullscreenChartPanel(e.ChartType);
        }
        
        private void OnShowSettingsPanel(ShowSettingsPanelEvent e)
        {
            ShowSettingsPanel();
        }
        
        private void OnHidePanel(HidePanelEvent e)
        {
            HidePanel(e.PanelName);
        }
        
        public void ShowPanel(string panelName, bool animate = true)
        {
            if (!uiPanels.ContainsKey(panelName) || uiPanels[panelName] == null)
            {
                Debug.LogWarning($"Panel '{panelName}' not found!");
                return;
            }
            
            // 隐藏当前活动面板
            if (currentActivePanel != panelName)
            {
                HidePanel(currentActivePanel, animate);
            }
            
            var panel = uiPanels[panelName];
            panel.SetActive(true);
            
            if (animate && enableUIAnimations)
            {
                PlayShowAnimation(panel);
            }
            
            currentActivePanel = panelName;
        }
        
        public void HidePanel(string panelName, bool animate = true)
        {
            if (!uiPanels.ContainsKey(panelName) || uiPanels[panelName] == null)
            {
                return;
            }
            
            var panel = uiPanels[panelName];
            
            if (animate && enableUIAnimations)
            {
                PlayHideAnimation(panel, () => panel.SetActive(false));
            }
            else
            {
                panel.SetActive(false);
            }
        }
        
        private void ShowLoadingPanel()
        {
            ShowPanel("loading");
        }
        
        private void HideLoadingPanel()
        {
            HidePanel("loading");
        }
        
        private void ShowMainPanel()
        {
            ShowPanel("main");
        }
        
        private void ShowErrorPanel()
        {
            ShowPanel("error");
        }
        
        private void HideErrorPanel()
        {
            HidePanel("error");
        }
        
        private void ShowSettingsPanel()
        {
            ShowPanel("settings");
        }
        
        private void ShowFullscreenChartPanel(ChartType chartType)
        {
            // 设置全屏图表的类型
            if (fullscreenChartPanel != null)
            {
                var fullscreenChart = fullscreenChartPanel.GetComponent<FullscreenChartPanel>();
                if (fullscreenChart != null)
                {
                    fullscreenChart.SetChartType(chartType);
                }
            }
            
            ShowPanel("fullscreen");
        }
        
        private void PlayShowAnimation(GameObject panel)
        {
            var canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = panel.AddComponent<CanvasGroup>();
            }
            
            canvasGroup.alpha = 0f;
            canvasGroup.DOFade(1f, panelTransitionDuration)
                .SetEase(panelTransitionEase);
            
            panel.transform.localScale = Vector3.zero;
            panel.transform.DOScale(Vector3.one, panelTransitionDuration)
                .SetEase(panelTransitionEase);
        }
        
        private void PlayHideAnimation(GameObject panel, System.Action onComplete)
        {
            var canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = panel.AddComponent<CanvasGroup>();
            }
            
            canvasGroup.DOFade(0f, panelTransitionDuration)
                .SetEase(panelTransitionEase);
            
            panel.transform.DOScale(Vector3.zero, panelTransitionDuration)
                .SetEase(panelTransitionEase)
                .OnComplete(() => onComplete?.Invoke());
        }
        
        private void Update()
        {
            // 检查屏幕尺寸变化
            if (autoAdjustLayout)
            {
                CheckScreenSizeChange();
            }
        }
        
        private void CheckScreenSizeChange()
        {
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            
            if (Vector2.Distance(currentScreenSize, lastScreenSize) > 10f)
            {
                lastScreenSize = currentScreenSize;
                AdjustLayoutForScreenSize();
            }
        }
        
        private void AdjustLayoutForScreenSize()
        {
            // 根据屏幕尺寸调整UI布局
            float screenRatio = (float)Screen.width / Screen.height;
            
            // 调整主面板布局
            if (mainPanel != null)
            {
                var mainRect = mainPanel.GetComponent<RectTransform>();
                if (mainRect != null)
                {
                    // 根据屏幕比例调整面板间距
                    if (screenRatio > 1.8f) // 超宽屏
                    {
                        // 增加左右面板宽度
                        AdjustForUltraWideScreen();
                    }
                    else if (screenRatio < 1.3f) // 接近正方形
                    {
                        // 调整为垂直布局
                        AdjustForSquareScreen();
                    }
                }
            }
        }
        
        private void AdjustForUltraWideScreen()
        {
            // 超宽屏适配逻辑
            Debug.Log("Adjusting for ultra-wide screen");
        }
        
        private void AdjustForSquareScreen()
        {
            // 正方形屏幕适配逻辑
            Debug.Log("Adjusting for square screen");
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        private void OnDestroy()
        {
            // 清理DOTween动画
            DOTween.KillAll();
        }
    }
    
    // UI事件定义
    public struct ShowFullscreenChartEvent
    {
        public ChartType ChartType;
    }
    
    public struct ShowSettingsPanelEvent
    {
    }
    
    public struct HidePanelEvent
    {
        public string PanelName;
    }
}
