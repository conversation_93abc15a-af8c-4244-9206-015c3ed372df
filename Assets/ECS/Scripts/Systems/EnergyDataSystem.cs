using System.Collections.Generic;
using UnityEngine;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源数据系统
    /// 负责生成和管理能源相关的业务数据
    /// 在MVP阶段，主要生成模拟的假数据
    /// </summary>
    public class EnergyDataSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            // 系统初始化逻辑
        }

        /// <summary>
        /// 生成模拟的KPI数据
        /// </summary>
        public List<KpiData> GenerateMockKpiData()
        {
            var kpiDataList = new List<KpiData>
            {
                new KpiData("总能耗", GenerateRandomValue(8000, 12000).ToString("F1"), "kWh", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType()),
                
                new KpiData("节省金额", GenerateRandomValue(15000, 25000).ToString("F0"), "元", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType()),
                
                new KpiData("碳排放", GenerateRandomValue(500, 800).ToString("F1"), "kg", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType()),
                
                new KpiData("能效分析", GenerateRandomValue(80, 95).ToString("F1"), "%", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType()),
                
                new KpiData("平均负荷率", GenerateRandomValue(70, 90).ToString("F1"), "%", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType()),
                
                new KpiData("节能目标完成度", GenerateRandomValue(85, 100).ToString("F1"), "%", 
                    GenerateRandomPercentage().ToString("F1"), "%", GetRandomTrendType())
            };

            return kpiDataList;
        }

        /// <summary>
        /// 生成模拟的图表数据
        /// </summary>
        public TimeSeriesChartData GenerateMockChartData()
        {
            var chartData = new TimeSeriesChartData();
            
            // 生成时间分类（最近12天）
            for (int i = 11; i >= 0; i--)
            {
                var date = System.DateTime.Now.AddDays(-i);
                chartData.Categories.Add(date.ToString("MM-dd"));
            }

            // 生成多个数据系列
            var seriesConfigs = new[]
            {
                new { Name = "总能耗", Color = "#5470c6", BaseValue = 800f, Variance = 200f },
                new { Name = "可再生能源", Color = "#91cc75", BaseValue = 300f, Variance = 100f },
                new { Name = "传统能源", Color = "#fac858", BaseValue = 500f, Variance = 150f },
                new { Name = "节能量", Color = "#ee6666", BaseValue = 150f, Variance = 50f },
                new { Name = "预测值", Color = "#73c0de", BaseValue = 750f, Variance = 180f }
            };

            foreach (var config in seriesConfigs)
            {
                var series = new ChartSeries(config.Name, new List<float>(), config.Color, ChartSeriesType.Bar);
                
                for (int i = 0; i < chartData.Categories.Count; i++)
                {
                    var value = config.BaseValue + Random.Range(-config.Variance, config.Variance);
                    series.Data.Add(Mathf.Max(0, value)); // 确保值不为负
                }
                
                chartData.Series.Add(series);
            }

            return chartData;
        }

        /// <summary>
        /// 生成模拟的筛选项数据
        /// </summary>
        public List<SimpleFilterItem> GenerateMockFilterItems()
        {
            var filterItems = new List<SimpleFilterItem>();

            // 添加分类筛选
            filterItems.Add(new SimpleFilterItem("cat_all", "全部设备", true, FilterItemType.Category));
            filterItems.Add(new SimpleFilterItem("cat_hvac", "空调系统", false, FilterItemType.Category));
            filterItems.Add(new SimpleFilterItem("cat_lighting", "照明系统", false, FilterItemType.Category));
            filterItems.Add(new SimpleFilterItem("cat_power", "动力设备", false, FilterItemType.Category));

            // 添加具体设备筛选
            var deviceConfigs = new[]
            {
                new { Id = "dev_001", Name = "中央空调A01", Parent = "cat_hvac" },
                new { Id = "dev_002", Name = "中央空调A02", Parent = "cat_hvac" },
                new { Id = "dev_003", Name = "LED照明01", Parent = "cat_lighting" },
                new { Id = "dev_004", Name = "LED照明02", Parent = "cat_lighting" },
                new { Id = "dev_005", Name = "电梯01", Parent = "cat_power" },
                new { Id = "dev_006", Name = "电梯02", Parent = "cat_power" },
                new { Id = "dev_007", Name = "水泵01", Parent = "cat_power" },
                new { Id = "dev_008", Name = "水泵02", Parent = "cat_power" }
            };

            foreach (var config in deviceConfigs)
            {
                filterItems.Add(new SimpleFilterItem(config.Id, config.Name, false, FilterItemType.Device, config.Parent));
            }

            return filterItems;
        }

        /// <summary>
        /// 根据筛选条件重新生成数据
        /// </summary>
        public void RefreshDataByFilters(List<string> selectedFilterIds)
        {
            var model = this.GetModel<EnergyDashboardModel>();
            
            // 设置加载状态
            model.SetLoadingState(true);
            
            // 模拟异步数据加载
            ActionKit.Delay(0.5f, () =>
            {
                // 根据筛选条件生成新的数据
                var newKpiData = GenerateMockKpiData();
                var newChartData = GenerateMockChartData();
                
                // 更新模型数据
                model.UpdateKpiData(newKpiData);
                model.UpdateChartData(newChartData);
                
                // 取消加载状态
                model.SetLoadingState(false);
            }).Start(this);
        }

        #region 私有辅助方法

        private float GenerateRandomValue(float min, float max)
        {
            return Random.Range(min, max);
        }

        private float GenerateRandomPercentage()
        {
            return Random.Range(-10f, 15f);
        }

        private KpiTrendType GetRandomTrendType()
        {
            var values = System.Enum.GetValues(typeof(KpiTrendType));
            return (KpiTrendType)values.GetValue(Random.Range(0, values.Length));
        }

        #endregion
    }
}
