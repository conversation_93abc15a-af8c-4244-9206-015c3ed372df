using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 选择筛选项命令
    /// 处理用户点击筛选项的业务逻辑
    /// </summary>
    public class SelectFilterItemCommand : AbstractCommand
    {
        private readonly string mFilterId;

        public SelectFilterItemCommand(string filterId)
        {
            mFilterId = filterId;
        }

        protected override void OnExecute()
        {
            var model = this.GetModel<EnergyDashboardModel>();
            var dataSystem = this.GetSystem<EnergyDataSystem>();

            // 更新筛选项的选中状态
            model.ToggleFilterItem(mFilterId);

            // 根据新的筛选条件重新获取数据
            var selectedFilterIds = model.SelectedFilterIds.Value;
            dataSystem.RefreshDataByFilters(selectedFilterIds);
        }
    }
}
