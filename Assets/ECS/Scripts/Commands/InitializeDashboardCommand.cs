using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 初始化看板命令
    /// 负责初始化看板的所有数据
    /// </summary>
    public class InitializeDashboardCommand : AbstractCommand
    {
        protected override void OnExecute()
        {
            var model = this.GetModel<EnergyDashboardModel>();
            var dataSystem = this.GetSystem<EnergyDataSystem>();

            // 设置加载状态
            model.SetLoadingState(true);

            // 初始化筛选项数据
            var filterItems = dataSystem.GenerateMockFilterItems();
            model.UpdateFilterItems(filterItems);

            // 模拟异步数据加载
            ActionKit.Delay(1f, () =>
            {
                // 生成初始数据
                var kpiData = dataSystem.GenerateMockKpiData();
                var chartData = dataSystem.GenerateMockChartData();

                // 更新模型数据
                model.UpdateKpiData(kpiData);
                model.UpdateChartData(chartData);

                // 取消加载状态
                model.SetLoadingState(false);
            }).Start(this);
        }
    }
}
