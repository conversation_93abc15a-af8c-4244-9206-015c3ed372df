using UnityEngine;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源看板测试脚本
    /// 用于测试MVP架构的各个组件是否正常工作
    /// </summary>
    public class EnergyDashboardTest : Mono<PERSON>ehaviour, IController
    {
        [Header("测试设置")]
        [SerializeField] private bool autoTest = true;
        [SerializeField] private float testInterval = 3f;

        private float mLastTestTime;

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        void Start()
        {
            // 初始化架构
            EnergyDashboardArchitecture.Interface.Init();
            
            // 注册事件监听
            this.RegisterEvent<EnergyDataUpdatedEvent>(OnEnergyDataUpdated);
            
            Debug.Log("[EnergyDashboardTest] 测试开始 - 架构初始化完成");
            
            // 执行初始化测试
            TestInitialization();
        }

        void Update()
        {
            if (autoTest && Time.time - mLastTestTime > testInterval)
            {
                mLastTestTime = Time.time;
                TestRandomDataGeneration();
            }
        }

        /// <summary>
        /// 测试初始化功能
        /// </summary>
        [ContextMenu("Test Initialization")]
        public void TestInitialization()
        {
            Debug.Log("[EnergyDashboardTest] 执行初始化测试");
            this.SendCommand<InitializeDashboardCommand>();
        }

        /// <summary>
        /// 测试随机数据生成
        /// </summary>
        [ContextMenu("Test Random Data Generation")]
        public void TestRandomDataGeneration()
        {
            Debug.Log("[EnergyDashboardTest] 执行随机数据生成测试");
            
            var dataSystem = this.GetSystem<EnergyDataSystem>();
            var model = this.GetModel<EnergyDashboardModel>();
            
            // 生成新的随机数据
            var newKpiData = dataSystem.GenerateMockKpiData();
            var newChartData = dataSystem.GenerateMockChartData();
            
            // 更新模型
            model.UpdateKpiData(newKpiData);
            model.UpdateChartData(newChartData);
        }

        /// <summary>
        /// 测试筛选功能
        /// </summary>
        [ContextMenu("Test Filter Selection")]
        public void TestFilterSelection()
        {
            Debug.Log("[EnergyDashboardTest] 执行筛选功能测试");
            
            var model = this.GetModel<EnergyDashboardModel>();
            var filterItems = model.FilterItems.Value;
            
            if (filterItems != null && filterItems.Count > 1)
            {
                // 随机选择一个筛选项
                var randomIndex = Random.Range(1, filterItems.Count);
                var randomFilterId = filterItems[randomIndex].Id;
                
                Debug.Log($"[EnergyDashboardTest] 选择筛选项: {filterItems[randomIndex].Name}");
                this.SendCommand(new SelectFilterItemCommand(randomFilterId));
            }
        }

        /// <summary>
        /// 测试数据查询
        /// </summary>
        [ContextMenu("Test Data Query")]
        public void TestDataQuery()
        {
            Debug.Log("[EnergyDashboardTest] 执行数据查询测试");
            
            var model = this.GetModel<EnergyDashboardModel>();
            
            // 查询当前数据状态
            var kpiCount = model.KpiDataList.Value?.Count ?? 0;
            var chartSeriesCount = model.MainChartData.Value?.Series?.Count ?? 0;
            var filterCount = model.FilterItems.Value?.Count ?? 0;
            var selectedFilterCount = model.SelectedFilterIds.Value?.Count ?? 0;
            var isLoading = model.IsLoading.Value;
            var lastUpdateTime = model.LastUpdateTime.Value;
            
            Debug.Log($"[EnergyDashboardTest] 数据状态查询结果:");
            Debug.Log($"  - KPI数量: {kpiCount}");
            Debug.Log($"  - 图表系列数量: {chartSeriesCount}");
            Debug.Log($"  - 筛选项数量: {filterCount}");
            Debug.Log($"  - 已选筛选项数量: {selectedFilterCount}");
            Debug.Log($"  - 加载状态: {isLoading}");
            Debug.Log($"  - 最后更新时间: {lastUpdateTime}");
        }

        /// <summary>
        /// 能源数据更新事件回调
        /// </summary>
        private void OnEnergyDataUpdated(EnergyDataUpdatedEvent e)
        {
            Debug.Log($"[EnergyDashboardTest] 收到数据更新事件: {e.UpdateReason}");
        }

        /// <summary>
        /// 打印架构信息
        /// </summary>
        [ContextMenu("Print Architecture Info")]
        public void PrintArchitectureInfo()
        {
            Debug.Log("[EnergyDashboardTest] 架构信息:");
            
            try
            {
                var model = this.GetModel<EnergyDashboardModel>();
                var system = this.GetSystem<EnergyDataSystem>();
                
                Debug.Log($"  - Model已注册: {model != null}");
                Debug.Log($"  - System已注册: {system != null}");
                Debug.Log($"  - 架构已初始化: {EnergyDashboardArchitecture.Interface != null}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 获取架构信息时出错: {ex.Message}");
            }
        }

        void OnDestroy()
        {
            // 清理事件监听
            this.UnRegisterEvent<EnergyDataUpdatedEvent>(OnEnergyDataUpdated);
        }
    }
}
