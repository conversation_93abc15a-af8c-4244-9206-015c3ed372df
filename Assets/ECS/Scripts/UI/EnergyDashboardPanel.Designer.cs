using System;
using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
	// Generate Id:d6c3a9c1-5c16-4de4-b26e-91365d317e3f
	public partial class EnergyDashboardPanel
	{
		public const string Name = "EnergyDashboardPanel";
		
		
		private EnergyDashboardPanelData mPrivateData = null;
		
		protected override void ClearUIComponents()
		{
			
			mData = null;
		}
		
		public EnergyDashboardPanelData Data
		{
			get
			{
				return mData;
			}
		}
		
		EnergyDashboardPanelData mData
		{
			get
			{
				return mPrivateData ?? (mPrivateData = new EnergyDashboardPanelData());
			}
			set
			{
				mUIData = value;
				mPrivateData = value;
			}
		}
	}
}
