using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System.Collections.Generic;
using XCharts.Runtime;

namespace QFramework.ShiCaiChang.LHJ
{
	public class EnergyDashboardPanelData : UIPanelData
	{
	}

	/// <summary>
	/// 能源看板面板 - MVP架构中的View层
	/// 负责界面显示和用户交互，通过QFramework架构与Model和System通信
	/// </summary>
	public partial class EnergyDashboardPanel : UIPanel, IController
	{
		[Header("KPI显示区域")]
		[SerializeField] private Transform kpiContainer;
		[SerializeField] private GameObject kpiItemPrefab;

		[Header("图表显示区域")]
		[SerializeField] private BaseChart mainChart;

		[Header("筛选区域")]
		[SerializeField] private Transform filterContainer;
		[SerializeField] private GameObject filterItemPrefab;

		[Header("状态显示")]
		[SerializeField] private GameObject loadingPanel;
		[SerializeField] private Text lastUpdateTimeText;

		// 数据绑定的注销器，用于清理事件监听
		private readonly List<IUnRegister> mUnRegisters = new List<IUnRegister>();

		// 当前显示的UI元素缓存
		private readonly List<GameObject> mKpiItems = new List<GameObject>();
		private readonly List<GameObject> mFilterItems = new List<GameObject>();

		public IArchitecture GetArchitecture()
		{
			return EnergyDashboardArchitecture.Interface;
		}

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as EnergyDashboardPanelData ?? new EnergyDashboardPanelData();

			// 初始化架构
			EnergyDashboardArchitecture.Interface.Init();

			// 绑定数据监听
			BindDataListeners();

			// 发送初始化命令
			this.SendCommand<InitializeDashboardCommand>();
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			// 面板打开时的逻辑
		}

		protected override void OnShow()
		{
			// 面板显示时的逻辑
		}

		protected override void OnHide()
		{
			// 面板隐藏时的逻辑
		}

		protected override void OnClose()
		{
			// 清理数据绑定
			UnbindDataListeners();
		}

		/// <summary>
		/// 绑定数据监听器
		/// </summary>
		private void BindDataListeners()
		{
			var model = this.GetModel<EnergyDashboardModel>();

			// 监听KPI数据变化
			mUnRegisters.Add(model.KpiDataList.Register(OnKpiDataChanged));

			// 监听图表数据变化
			mUnRegisters.Add(model.MainChartData.Register(OnChartDataChanged));

			// 监听筛选项数据变化
			mUnRegisters.Add(model.FilterItems.Register(OnFilterItemsChanged));

			// 监听加载状态变化
			mUnRegisters.Add(model.IsLoading.Register(OnLoadingStateChanged));

			// 监听最后更新时间变化
			mUnRegisters.Add(model.LastUpdateTime.Register(OnLastUpdateTimeChanged));
		}

		/// <summary>
		/// 解绑数据监听器
		/// </summary>
		private void UnbindDataListeners()
		{
			foreach (var unRegister in mUnRegisters)
			{
				unRegister?.UnRegister();
			}
			mUnRegisters.Clear();
		}

		#region 数据变化回调方法

		/// <summary>
		/// KPI数据变化回调
		/// </summary>
		private void OnKpiDataChanged(List<KpiData> kpiDataList)
		{
			UpdateKpiDisplay(kpiDataList);
		}

		/// <summary>
		/// 图表数据变化回调
		/// </summary>
		private void OnChartDataChanged(TimeSeriesChartData chartData)
		{
			UpdateChartDisplay(chartData);
		}

		/// <summary>
		/// 筛选项数据变化回调
		/// </summary>
		private void OnFilterItemsChanged(List<SimpleFilterItem> filterItems)
		{
			UpdateFilterDisplay(filterItems);
		}

		/// <summary>
		/// 加载状态变化回调
		/// </summary>
		private void OnLoadingStateChanged(bool isLoading)
		{
			if (loadingPanel != null)
			{
				loadingPanel.SetActive(isLoading);
			}
		}

		/// <summary>
		/// 最后更新时间变化回调
		/// </summary>
		private void OnLastUpdateTimeChanged(string lastUpdateTime)
		{
			if (lastUpdateTimeText != null && !string.IsNullOrEmpty(lastUpdateTime))
			{
				lastUpdateTimeText.text = $"最后更新: {lastUpdateTime}";
			}
		}

		#endregion

		#region UI更新方法

		/// <summary>
		/// 更新KPI显示
		/// </summary>
		private void UpdateKpiDisplay(List<KpiData> kpiDataList)
		{
			if (kpiContainer == null || kpiItemPrefab == null) return;

			// 清理现有的KPI项
			ClearKpiItems();

			// 创建新的KPI项
			foreach (var kpiData in kpiDataList)
			{
				var kpiItem = Instantiate(kpiItemPrefab, kpiContainer);
				SetupKpiItem(kpiItem, kpiData);
				mKpiItems.Add(kpiItem);
			}
		}

		/// <summary>
		/// 设置KPI项的数据显示
		/// </summary>
		private void SetupKpiItem(GameObject kpiItem, KpiData kpiData)
		{
			// 查找子组件并设置数据
			var titleText = kpiItem.transform.Find("TitleText")?.GetComponent<Text>();
			var valueText = kpiItem.transform.Find("ValueText")?.GetComponent<Text>();
			var unitText = kpiItem.transform.Find("UnitText")?.GetComponent<Text>();
			var subValueText = kpiItem.transform.Find("SubValueText")?.GetComponent<Text>();
			var trendIcon = kpiItem.transform.Find("TrendIcon")?.GetComponent<Image>();

			if (titleText != null) titleText.text = kpiData.Title;
			if (valueText != null) valueText.text = kpiData.Value;
			if (unitText != null) unitText.text = kpiData.Unit;
			if (subValueText != null)
			{
				var subText = !string.IsNullOrEmpty(kpiData.SubValue) ?
					$"{kpiData.SubValue}{kpiData.SubUnit}" : "";
				subValueText.text = subText;
			}

			// 设置趋势图标颜色
			if (trendIcon != null)
			{
				switch (kpiData.TrendType)
				{
					case KpiTrendType.Up:
						trendIcon.color = Color.green;
						break;
					case KpiTrendType.Down:
						trendIcon.color = Color.red;
						break;
					case KpiTrendType.Stable:
						trendIcon.color = Color.gray;
						break;
				}
			}
		}

		/// <summary>
		/// 更新图表显示
		/// </summary>
		private void UpdateChartDisplay(TimeSeriesChartData chartData)
		{
			if (mainChart == null) return;

			// 清空现有数据
			mainChart.RemoveData();

			// 设置X轴分类
			if (chartData.Categories != null && chartData.Categories.Count > 0)
			{
				var xAxis = mainChart.EnsureChartComponent<XAxis>();
				xAxis.data.Clear();
				foreach (var category in chartData.Categories)
				{
					xAxis.data.Add(category);
				}
			}

			// 添加数据系列
			if (chartData.Series != null)
			{
				foreach (var seriesData in chartData.Series)
				{
					var serie = mainChart.AddSerie<Bar>(seriesData.Name);
					serie.itemStyle.color = ColorUtility.TryParseHtmlString(seriesData.Color, out var color) ?
						color : Color.blue;

					// 添加数据点
					if (seriesData.Data != null)
					{
						foreach (var value in seriesData.Data)
						{
							mainChart.AddData(serie.index, value);
						}
					}
				}
			}

			// 刷新图表
			mainChart.RefreshChart();
		}

		/// <summary>
		/// 更新筛选项显示
		/// </summary>
		private void UpdateFilterDisplay(List<SimpleFilterItem> filterItems)
		{
			if (filterContainer == null || filterItemPrefab == null) return;

			// 清理现有的筛选项
			ClearFilterItems();

			// 创建新的筛选项
			foreach (var filterItem in filterItems)
			{
				var filterItemObj = Instantiate(filterItemPrefab, filterContainer);
				SetupFilterItem(filterItemObj, filterItem);
				mFilterItems.Add(filterItemObj);
			}
		}

		/// <summary>
		/// 设置筛选项的数据显示和交互
		/// </summary>
		private void SetupFilterItem(GameObject filterItemObj, SimpleFilterItem filterData)
		{
			var nameText = filterItemObj.transform.Find("NameText")?.GetComponent<Text>();
			var toggle = filterItemObj.GetComponent<Toggle>();
			var button = filterItemObj.GetComponent<Button>();

			if (nameText != null) nameText.text = filterData.Name;

			// 设置选中状态
			if (toggle != null)
			{
				toggle.isOn = filterData.IsSelected;
				toggle.onValueChanged.AddListener((isOn) => OnFilterItemClicked(filterData.Id));
			}
			else if (button != null)
			{
				button.onClick.AddListener(() => OnFilterItemClicked(filterData.Id));
			}
		}

		/// <summary>
		/// 筛选项点击回调
		/// </summary>
		private void OnFilterItemClicked(string filterId)
		{
			this.SendCommand(new SelectFilterItemCommand(filterId));
		}

		/// <summary>
		/// 清理KPI项
		/// </summary>
		private void ClearKpiItems()
		{
			foreach (var item in mKpiItems)
			{
				if (item != null) DestroyImmediate(item);
			}
			mKpiItems.Clear();
		}

		/// <summary>
		/// 清理筛选项
		/// </summary>
		private void ClearFilterItems()
		{
			foreach (var item in mFilterItems)
			{
				if (item != null) DestroyImmediate(item);
			}
			mFilterItems.Clear();
		}

		#endregion
	}
}
