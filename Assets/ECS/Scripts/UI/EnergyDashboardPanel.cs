using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
	public class EnergyDashboardPanelData : UIPanelData
	{
	}
	public partial class EnergyDashboardPanel : UIPanel
	{
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as EnergyDashboardPanelData ?? new EnergyDashboardPanelData();
			// please add init code here
		}
		
		protected override void OnOpen(IUIData uiData = null)
		{
		}
		
		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
		}
	}
}
