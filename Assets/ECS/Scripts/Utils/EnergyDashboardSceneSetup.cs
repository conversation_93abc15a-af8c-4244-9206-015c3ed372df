using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源看板场景设置工具
    /// 用于快速创建和配置测试场景
    /// </summary>
    public class EnergyDashboardSceneSetup : MonoBehaviour
    {
        [Header("自动设置选项")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createTestUI = true;
        [SerializeField] private bool addTestScript = true;

        void Start()
        {
            if (autoSetupOnStart)
            {
                SetupScene();
            }
        }

        /// <summary>
        /// 设置场景
        /// </summary>
        [ContextMenu("Setup Scene")]
        public void SetupScene()
        {
            Debug.Log("[EnergyDashboardSceneSetup] 开始设置场景...");

            // 确保有Canvas
            EnsureCanvas();

            // 创建测试UI
            if (createTestUI)
            {
                CreateTestUI();
            }

            // 添加测试脚本
            if (addTestScript)
            {
                AddTestScript();
            }

            Debug.Log("[EnergyDashboardSceneSetup] 场景设置完成！");
        }

        /// <summary>
        /// 确保场景中有Canvas
        /// </summary>
        private void EnsureCanvas()
        {
            var canvas = FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                var canvasObj = new GameObject("Canvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();

                Debug.Log("[EnergyDashboardSceneSetup] 创建了Canvas");
            }
        }

        /// <summary>
        /// 创建测试UI
        /// </summary>
        private void CreateTestUI()
        {
            var canvas = FindObjectOfType<Canvas>();
            if (canvas == null) return;

            // 创建主面板
            var panelObj = CreateUIPanel(canvas.transform, "EnergyDashboardPanel");
            
            // 创建KPI容器
            var kpiContainer = CreateUIContainer(panelObj.transform, "KpiContainer");
            SetRectTransform(kpiContainer.GetComponent<RectTransform>(), 
                new Vector2(0, 0.7f), new Vector2(1, 1f), Vector2.zero);

            // 创建图表容器
            var chartContainer = CreateUIContainer(panelObj.transform, "ChartContainer");
            SetRectTransform(chartContainer.GetComponent<RectTransform>(), 
                new Vector2(0, 0.3f), new Vector2(1, 0.7f), Vector2.zero);

            // 创建筛选容器
            var filterContainer = CreateUIContainer(panelObj.transform, "FilterContainer");
            SetRectTransform(filterContainer.GetComponent<RectTransform>(), 
                new Vector2(0, 0f), new Vector2(1, 0.3f), Vector2.zero);

            // 创建加载面板
            var loadingPanel = CreateLoadingPanel(panelObj.transform);

            // 创建状态栏
            var statusBar = CreateStatusBar(panelObj.transform);

            // 创建示例预制体
            CreateKpiItemPrefab();
            CreateFilterItemPrefab();

            Debug.Log("[EnergyDashboardSceneSetup] 创建了测试UI结构");
        }

        /// <summary>
        /// 创建UI面板
        /// </summary>
        private GameObject CreateUIPanel(Transform parent, string name)
        {
            var panelObj = new GameObject(name);
            panelObj.transform.SetParent(parent, false);

            var rectTransform = panelObj.AddComponent<RectTransform>();
            SetRectTransform(rectTransform, Vector2.zero, Vector2.one, Vector2.zero);

            var image = panelObj.AddComponent<Image>();
            image.color = new Color(0.1f, 0.1f, 0.1f, 0.8f);

            return panelObj;
        }

        /// <summary>
        /// 创建UI容器
        /// </summary>
        private GameObject CreateUIContainer(Transform parent, string name)
        {
            var containerObj = new GameObject(name);
            containerObj.transform.SetParent(parent, false);

            var rectTransform = containerObj.AddComponent<RectTransform>();
            
            // 添加布局组件
            var layoutGroup = containerObj.AddComponent<HorizontalLayoutGroup>();
            layoutGroup.childControlWidth = true;
            layoutGroup.childControlHeight = true;
            layoutGroup.childForceExpandWidth = true;
            layoutGroup.childForceExpandHeight = true;
            layoutGroup.spacing = 10f;
            layoutGroup.padding = new RectOffset(10, 10, 10, 10);

            return containerObj;
        }

        /// <summary>
        /// 创建加载面板
        /// </summary>
        private GameObject CreateLoadingPanel(Transform parent)
        {
            var loadingObj = new GameObject("LoadingPanel");
            loadingObj.transform.SetParent(parent, false);

            var rectTransform = loadingObj.AddComponent<RectTransform>();
            SetRectTransform(rectTransform, Vector2.zero, Vector2.one, Vector2.zero);

            var image = loadingObj.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0.5f);

            // 添加加载文本
            var textObj = new GameObject("LoadingText");
            textObj.transform.SetParent(loadingObj.transform, false);
            
            var textRect = textObj.AddComponent<RectTransform>();
            SetRectTransform(textRect, Vector2.zero, Vector2.one, Vector2.zero);
            
            var text = textObj.AddComponent<Text>();
            text.text = "加载中...";
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 24;
            text.color = Color.white;
            text.alignment = TextAnchor.MiddleCenter;

            loadingObj.SetActive(false);
            return loadingObj;
        }

        /// <summary>
        /// 创建状态栏
        /// </summary>
        private GameObject CreateStatusBar(Transform parent)
        {
            var statusBarObj = new GameObject("StatusBar");
            statusBarObj.transform.SetParent(parent, false);

            var rectTransform = statusBarObj.AddComponent<RectTransform>();
            SetRectTransform(rectTransform, new Vector2(0, 0), new Vector2(1, 0.05f), Vector2.zero);

            var image = statusBarObj.AddComponent<Image>();
            image.color = new Color(0.2f, 0.2f, 0.2f, 1f);

            // 添加更新时间文本
            var textObj = new GameObject("LastUpdateTimeText");
            textObj.transform.SetParent(statusBarObj.transform, false);
            
            var textRect = textObj.AddComponent<RectTransform>();
            SetRectTransform(textRect, Vector2.zero, Vector2.one, Vector2.zero);
            
            var text = textObj.AddComponent<Text>();
            text.text = "最后更新: --";
            text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
            text.fontSize = 12;
            text.color = Color.white;
            text.alignment = TextAnchor.MiddleRight;

            return statusBarObj;
        }

        /// <summary>
        /// 创建KPI项预制体
        /// </summary>
        private void CreateKpiItemPrefab()
        {
            // 这里可以创建KPI项的预制体结构
            // 实际项目中应该通过预制体系统来管理
            Debug.Log("[EnergyDashboardSceneSetup] KPI预制体需要手动创建");
        }

        /// <summary>
        /// 创建筛选项预制体
        /// </summary>
        private void CreateFilterItemPrefab()
        {
            // 这里可以创建筛选项的预制体结构
            // 实际项目中应该通过预制体系统来管理
            Debug.Log("[EnergyDashboardSceneSetup] 筛选项预制体需要手动创建");
        }

        /// <summary>
        /// 添加测试脚本
        /// </summary>
        private void AddTestScript()
        {
            var testScript = FindObjectOfType<EnergyDashboardTest>();
            if (testScript == null)
            {
                var testObj = new GameObject("EnergyDashboardTest");
                testObj.AddComponent<EnergyDashboardTest>();
                Debug.Log("[EnergyDashboardSceneSetup] 添加了测试脚本");
            }
        }

        /// <summary>
        /// 设置RectTransform
        /// </summary>
        private void SetRectTransform(RectTransform rectTransform, Vector2 anchorMin, Vector2 anchorMax, Vector2 anchoredPosition)
        {
            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
            rectTransform.anchoredPosition = anchoredPosition;
            rectTransform.sizeDelta = Vector2.zero;
        }
    }
}
