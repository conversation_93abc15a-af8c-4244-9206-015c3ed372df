using System.Collections.Generic;
using UnityEngine;
using QFramework;
using System.IO;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源数据导出工具
    /// 用于将模拟数据导出为JSON格式，便于调试和数据分析
    /// </summary>
    public class EnergyDataExporter : MonoBehaviour, IController
    {
        [Header("导出设置")]
        [SerializeField] private string exportPath = "EnergyData";
        [SerializeField] private bool exportOnStart = false;
        [SerializeField] private bool autoExportInterval = false;
        [SerializeField] private float exportInterval = 60f;

        private float mLastExportTime;

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        void Start()
        {
            // 确保架构已初始化
            EnergyDashboardArchitecture.Interface.Init();

            if (exportOnStart)
            {
                ExportCurrentData();
            }
        }

        void Update()
        {
            if (autoExportInterval && Time.time - mLastExportTime > exportInterval)
            {
                mLastExportTime = Time.time;
                ExportCurrentData();
            }
        }

        /// <summary>
        /// 导出当前数据
        /// </summary>
        [ContextMenu("Export Current Data")]
        public void ExportCurrentData()
        {
            try
            {
                var model = this.GetModel<EnergyDashboardModel>();
                
                // 创建导出数据结构
                var exportData = new EnergyDataExport
                {
                    ExportTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    KpiData = model.KpiDataList.Value ?? new List<KpiData>(),
                    ChartData = model.MainChartData.Value ?? new TimeSeriesChartData(),
                    FilterItems = model.FilterItems.Value ?? new List<SimpleFilterItem>(),
                    SelectedFilterIds = model.SelectedFilterIds.Value ?? new List<string>(),
                    IsLoading = model.IsLoading.Value,
                    LastUpdateTime = model.LastUpdateTime.Value ?? ""
                };

                // 转换为JSON
                var json = JsonUtility.ToJson(exportData, true);
                
                // 保存到文件
                var fileName = $"EnergyData_{System.DateTime.Now:yyyyMMdd_HHmmss}.json";
                var fullPath = Path.Combine(Application.persistentDataPath, exportPath, fileName);
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                File.WriteAllText(fullPath, json);
                
                Debug.Log($"[EnergyDataExporter] 数据已导出到: {fullPath}");
                Debug.Log($"[EnergyDataExporter] 导出数据摘要:");
                Debug.Log($"  - KPI数量: {exportData.KpiData.Count}");
                Debug.Log($"  - 图表系列数量: {exportData.ChartData.Series?.Count ?? 0}");
                Debug.Log($"  - 筛选项数量: {exportData.FilterItems.Count}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDataExporter] 导出数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出模拟数据样本
        /// </summary>
        [ContextMenu("Export Sample Data")]
        public void ExportSampleData()
        {
            try
            {
                var dataSystem = this.GetSystem<EnergyDataSystem>();
                
                // 生成样本数据
                var sampleData = new EnergyDataExport
                {
                    ExportTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    KpiData = dataSystem.GenerateMockKpiData(),
                    ChartData = dataSystem.GenerateMockChartData(),
                    FilterItems = dataSystem.GenerateMockFilterItems(),
                    SelectedFilterIds = new List<string>(),
                    IsLoading = false,
                    LastUpdateTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                // 转换为JSON
                var json = JsonUtility.ToJson(sampleData, true);
                
                // 保存到文件
                var fileName = "EnergyData_Sample.json";
                var fullPath = Path.Combine(Application.persistentDataPath, exportPath, fileName);
                
                // 确保目录存在
                var directory = Path.GetDirectoryName(fullPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                File.WriteAllText(fullPath, json);
                
                Debug.Log($"[EnergyDataExporter] 样本数据已导出到: {fullPath}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDataExporter] 导出样本数据时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 打开导出目录
        /// </summary>
        [ContextMenu("Open Export Directory")]
        public void OpenExportDirectory()
        {
            var fullPath = Path.Combine(Application.persistentDataPath, exportPath);
            
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }
            
            Application.OpenURL("file://" + fullPath);
            Debug.Log($"[EnergyDataExporter] 打开导出目录: {fullPath}");
        }

        /// <summary>
        /// 清理导出文件
        /// </summary>
        [ContextMenu("Clear Export Files")]
        public void ClearExportFiles()
        {
            try
            {
                var fullPath = Path.Combine(Application.persistentDataPath, exportPath);
                
                if (Directory.Exists(fullPath))
                {
                    var files = Directory.GetFiles(fullPath, "*.json");
                    foreach (var file in files)
                    {
                        File.Delete(file);
                    }
                    
                    Debug.Log($"[EnergyDataExporter] 已清理 {files.Length} 个导出文件");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDataExporter] 清理导出文件时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 能源数据导出结构
    /// </summary>
    [System.Serializable]
    public class EnergyDataExport
    {
        public string ExportTime;
        public List<KpiData> KpiData;
        public TimeSeriesChartData ChartData;
        public List<SimpleFilterItem> FilterItems;
        public List<string> SelectedFilterIds;
        public bool IsLoading;
        public string LastUpdateTime;
    }
}
