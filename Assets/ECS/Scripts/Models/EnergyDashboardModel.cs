using System.Collections.Generic;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源看板数据模型
    /// 作为唯一的数据中心，负责存储和管理界面所需的所有状态和数据
    /// </summary>
    public class EnergyDashboardModel : AbstractModel
    {
        // KPI数据
        public BindableProperty<List<KpiData>> KpiDataList { get; private set; }
        
        // 图表数据
        public BindableProperty<TimeSeriesChartData> MainChartData { get; private set; }
        
        // 筛选列表数据
        public BindableProperty<List<SimpleFilterItem>> FilterItems { get; private set; }
        
        // 当前选中的筛选条件
        public BindableProperty<List<string>> SelectedFilterIds { get; private set; }
        
        // 数据加载状态
        public BindableProperty<bool> IsLoading { get; private set; }
        
        // 最后更新时间
        public BindableProperty<string> LastUpdateTime { get; private set; }

        protected override void OnInit()
        {
            // 初始化所有绑定属性
            KpiDataList = new BindableProperty<List<KpiData>>(new List<KpiData>());
            MainChartData = new BindableProperty<TimeSeriesChartData>(new TimeSeriesChartData());
            FilterItems = new BindableProperty<List<SimpleFilterItem>>(new List<SimpleFilterItem>());
            SelectedFilterIds = new BindableProperty<List<string>>(new List<string>());
            IsLoading = new BindableProperty<bool>(false);
            LastUpdateTime = new BindableProperty<string>("");

            // 监听数据变化，当数据更新时发送事件
            KpiDataList.Register(OnDataChanged);
            MainChartData.Register(OnDataChanged);
            FilterItems.Register(OnDataChanged);
        }

        private void OnDataChanged<T>(T value)
        {
            // 更新最后更新时间
            LastUpdateTime.Value = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            // 发送数据更新事件
            this.SendEvent<EnergyDataUpdatedEvent>();
        }

        /// <summary>
        /// 更新KPI数据
        /// </summary>
        public void UpdateKpiData(List<KpiData> newKpiData)
        {
            KpiDataList.Value = newKpiData ?? new List<KpiData>();
        }

        /// <summary>
        /// 更新图表数据
        /// </summary>
        public void UpdateChartData(TimeSeriesChartData newChartData)
        {
            MainChartData.Value = newChartData ?? new TimeSeriesChartData();
        }

        /// <summary>
        /// 更新筛选项数据
        /// </summary>
        public void UpdateFilterItems(List<SimpleFilterItem> newFilterItems)
        {
            FilterItems.Value = newFilterItems ?? new List<SimpleFilterItem>();
        }

        /// <summary>
        /// 切换筛选项选中状态
        /// </summary>
        public void ToggleFilterItem(string filterId)
        {
            var filterItems = FilterItems.Value;
            var targetItem = filterItems.Find(item => item.Id == filterId);
            
            if (targetItem != null)
            {
                targetItem.IsSelected = !targetItem.IsSelected;
                
                // 更新选中的筛选条件列表
                var selectedIds = SelectedFilterIds.Value;
                if (targetItem.IsSelected)
                {
                    if (!selectedIds.Contains(filterId))
                    {
                        selectedIds.Add(filterId);
                    }
                }
                else
                {
                    selectedIds.Remove(filterId);
                }
                
                // 触发更新
                FilterItems.Value = filterItems;
                SelectedFilterIds.Value = selectedIds;
            }
        }

        /// <summary>
        /// 设置加载状态
        /// </summary>
        public void SetLoadingState(bool isLoading)
        {
            IsLoading.Value = isLoading;
        }

        protected override void OnDeinit()
        {
            // 清理资源
            KpiDataList?.Deinit();
            MainChartData?.Deinit();
            FilterItems?.Deinit();
            SelectedFilterIds?.Deinit();
            IsLoading?.Deinit();
            LastUpdateTime?.Deinit();
        }
    }
}
