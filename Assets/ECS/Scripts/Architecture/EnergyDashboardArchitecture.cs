using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源看板架构类
    /// 负责注册和管理整个能源看板模块的所有组件
    /// </summary>
    public class EnergyDashboardArchitecture : Architecture<EnergyDashboardArchitecture>
    {
        protected override void Init()
        {
            // 注册数据模型
            this.RegisterModel<EnergyDashboardModel>(new EnergyDashboardModel());

            // 注册业务系统
            this.RegisterSystem<EnergyDataSystem>(new EnergyDataSystem());
        }
    }
}
