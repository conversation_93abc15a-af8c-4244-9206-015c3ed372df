using System;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// KPI数据结构，用于存储各种关键指标信息
    /// </summary>
    [Serializable]
    public class KpiData
    {
        public string Title { get; set; }
        public string Value { get; set; }
        public string Unit { get; set; }
        public string SubValue { get; set; }  // 副值，如同比、环比等
        public string SubUnit { get; set; }   // 副值单位
        public KpiTrendType TrendType { get; set; }
        public string IconPath { get; set; }  // 图标路径

        public KpiData()
        {
            Title = "";
            Value = "0";
            Unit = "";
            SubValue = "";
            SubUnit = "";
            TrendType = KpiTrendType.Stable;
            IconPath = "";
        }

        public KpiData(string title, string value, string unit = "", string subValue = "", string subUnit = "", KpiTrendType trendType = KpiTrendType.Stable, string iconPath = "")
        {
            Title = title;
            Value = value;
            Unit = unit;
            SubValue = subValue;
            SubUnit = subUnit;
            TrendType = trendType;
            IconPath = iconPath;
        }
    }

    /// <summary>
    /// KPI趋势类型
    /// </summary>
    public enum KpiTrendType
    {
        Up,      // 上升
        Down,    // 下降
        Stable   // 稳定
    }
}
