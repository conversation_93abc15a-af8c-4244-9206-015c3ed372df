using System;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 简单筛选项数据结构
    /// </summary>
    [Serializable]
    public class SimpleFilterItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public bool IsSelected { get; set; }
        public FilterItemType Type { get; set; }
        public string ParentId { get; set; }  // 父级ID，用于树形结构

        public SimpleFilterItem()
        {
            Id = "";
            Name = "";
            IsSelected = false;
            Type = FilterItemType.Device;
            ParentId = "";
        }

        public SimpleFilterItem(string id, string name, bool isSelected = false, FilterItemType type = FilterItemType.Device, string parentId = "")
        {
            Id = id;
            Name = name;
            IsSelected = isSelected;
            Type = type;
            ParentId = parentId;
        }
    }

    /// <summary>
    /// 筛选项类型
    /// </summary>
    public enum FilterItemType
    {
        Category,    // 分类
        Device,      // 设备
        Location,    // 位置
        TimeRange    // 时间范围
    }
}
