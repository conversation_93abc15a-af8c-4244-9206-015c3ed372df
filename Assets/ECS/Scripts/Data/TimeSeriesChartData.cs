using System;
using System.Collections.Generic;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 时间序列图表数据结构，用于XCharts图表
    /// </summary>
    [Serializable]
    public class TimeSeriesChartData
    {
        public List<string> Categories { get; set; }  // X轴分类（时间）
        public List<ChartSeries> Series { get; set; }  // 数据系列

        public TimeSeriesChartData()
        {
            Categories = new List<string>();
            Series = new List<ChartSeries>();
        }
    }

    /// <summary>
    /// 图表数据系列
    /// </summary>
    [Serializable]
    public class ChartSeries
    {
        public string Name { get; set; }           // 系列名称
        public List<float> Data { get; set; }      // 数据值
        public string Color { get; set; }          // 颜色
        public ChartSeriesType Type { get; set; }  // 图表类型

        public ChartSeries()
        {
            Name = "";
            Data = new List<float>();
            Color = "#5470c6";
            Type = ChartSeriesType.Bar;
        }

        public ChartSeries(string name, List<float> data, string color = "#5470c6", ChartSeriesType type = ChartSeriesType.Bar)
        {
            Name = name;
            Data = data ?? new List<float>();
            Color = color;
            Type = type;
        }
    }

    /// <summary>
    /// 图表系列类型
    /// </summary>
    public enum ChartSeriesType
    {
        Bar,     // 柱状图
        Line,    // 折线图
        Area,    // 面积图
        Pie      // 饼图
    }
}
